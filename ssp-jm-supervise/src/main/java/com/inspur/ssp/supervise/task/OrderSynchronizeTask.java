package com.inspur.ssp.supervise.task;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.inspur.ssp.bsp.entity.PubUser;
import com.inspur.ssp.bsp.service.IPubUserService;
import com.inspur.ssp.supervise.bean.entity.*;
import com.inspur.ssp.supervise.service.*;
import com.inspur.ssp.supervise.service.impl.OperationLogServiceImpl;
import com.inspur.ssp.supervise.service.impl.SupOrderItemServiceImpl;
import com.inspur.ssp.supervise.utils.HistoryTimeUtils;
import org.jangod.iweb.util.Tools;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;

import static org.jangod.iweb.util.Tools.genId;

/**
 * <AUTHOR>
 * @Date 2024 07 30 10 04
 **/
@Service("orderSynchronizeTask")
public class OrderSynchronizeTask {

    private Logger logger = LoggerFactory.getLogger(OrderSynchronizeTask.class);

    @Autowired
    @Qualifier("interfaceBatchServiceImpl")
    private IInterfaceBatchService iInterfaceBatchService;
    @Autowired
    @Qualifier("interfaceLogServiceImpl")
    private IInterfaceLogService iInterfaceLogService;

    @Autowired
    private ISupOrderService supOrderServiceImpl;

    @Autowired
    private ISupDeliveryItemService supDeliveryItemServiceImpl;

    @Autowired
    private ISupStockInItemService supStockInItemServiceImpl;

    @Autowired
    private ISupInvoiceItemService supInvoiceItemServiceImpl;

    @Autowired
    private ISupOfficialService supOfficialServiceImpl;

    @Autowired
    private ISupOfficialReceiveService supOfficialReceiveServiceImpl;

    @Resource
    private ISupJobService supJobServiceImpl;

    @Resource
    private OperationLogServiceImpl operationLogService;



    @Autowired
    @Qualifier("dealFailSZInterfaceTaskImpl")
    private DealFailSZInterfaceTask dealFailSZInterfaceTaskImpl;

    @Autowired
    @Qualifier("dealFailGDInterfaceTaskImpl")
    private DealFailGDInterfaceTask dealFailGDInterfaceTaskImpl;

    @Autowired
    @Qualifier("shenZhenInterfaceTaskImpl")
    private ShenZhenInterfaceTask shenZhenInterfaceTaskImpl;

    @Autowired
    @Qualifier("gdInterfaceTaskImpl")
    private GDInterfaceTask gdInterfaceTaskImpl;

    @Autowired
    @Qualifier("gzInterfaceTaskImpl")
    private GZInterfaceTask gzInterfaceTaskImpl;

    @Autowired
    private IPubUserService pubUserServiceImpl;
    @Autowired
    private SupOrderItemServiceImpl supOrderItemServiceImpl;

    /**
     *
     * 由于对接方，存在数据未同步过来的情况
     * 每周再同步一次本周内的订单、配送、入库、发票数据
     */
    public void orderSynchronizeDealDataNew(){
        Set<String> shenzhenOrderNumSet = new HashSet<>();//深圳订单、配送、入库、发票相关订单都放里面
        Set<String> gdOrderNumSet = new HashSet<>();//广东订单、配送、入库、发票相关订单都放里面
        Map<String, Object> shenzhenMap = new HashMap<>();
        Map<String, Object> gdMap = new HashMap<>();
        StringBuilder content = new StringBuilder(3000);
        SimpleDateFormat dft = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        //订单数据
        QueryWrapper<SupOrder> supOrderItemQueryWrapper = new QueryWrapper<>();
        supOrderItemQueryWrapper.ge("SUBMIT_TIME",this.historyTime7());
        supOrderItemQueryWrapper.in("SOURCE","1","2");
        List<SupOrder> SupOrderItemList = supOrderServiceImpl.list(supOrderItemQueryWrapper);
        int supOrderItemListCountStart = SupOrderItemList.size();
        int supOrderItemShenzhenCountStart = 0;
        int supOrderItemGdCountStart = 0;
        content.append("同步本周内的药品、订单、配送、入库、发票数据信息如下："+"\n ");
        content.append("（未同步前）近7天订单明细总数:"+supOrderItemListCountStart);
        for (SupOrder order:SupOrderItemList) {
            if(order.getOrderNum().startsWith("D")){
                shenzhenOrderNumSet.add(order.getOrderNum());
                supOrderItemShenzhenCountStart++;
            }
            if(order.getOrderNum().startsWith("22")){
                gdOrderNumSet.add(order.getOrderNum());
                supOrderItemGdCountStart++;
            }
        }
        content.append("（未同步前）近7天深圳平台订单明细总数:"+supOrderItemShenzhenCountStart);
        content.append("（未同步前）近7天广东平台订单明细总数:"+supOrderItemGdCountStart+"\n ");

        //配送数据
        QueryWrapper<SupDeliveryItem> supDeliveryItemQueryWrapper = new QueryWrapper<>();
        supDeliveryItemQueryWrapper.ge("DELIVERY_TIME",this.historyTime7());
        List<SupDeliveryItem> supDeliveryItemList = supDeliveryItemServiceImpl.list(supDeliveryItemQueryWrapper);
        int supDeliveryItemListCountStart = supDeliveryItemList.size();
        int supDeliveryItemShenzhenCountStart = 0;
        int supDeliveryItemGdCountStart = 0;
        content.append("（未同步前）近7天配送明细总数:"+supDeliveryItemListCountStart);
        for (SupDeliveryItem order:supDeliveryItemList) {
            if(order.getOrderCode().startsWith("D")){//深圳平台订单以D开头
                shenzhenOrderNumSet.add(order.getOrderCode());
                supDeliveryItemShenzhenCountStart++;
            }
            if(order.getOrderCode().startsWith("22")){//省平台订单以22开头
                gdOrderNumSet.add(order.getOrderCode());
                supDeliveryItemGdCountStart++;
            }
        }
        content.append("（未同步前）近7天深圳平台配送明细总数:"+supDeliveryItemShenzhenCountStart);
        content.append("（未同步前）近7天广东平台配送明细总数:"+supDeliveryItemGdCountStart+"\n ");

        //入库数据
        QueryWrapper<SupStockInItem> supStockInItemQueryWrapper = new QueryWrapper<>();
        supStockInItemQueryWrapper.ge("STOCK_IN_TIME",this.historyTime7());
        List<SupStockInItem> supStockInItemList = supStockInItemServiceImpl.list(supStockInItemQueryWrapper);
        int supStockInItemListCountStart = supStockInItemList.size();
        int supStockInItemShenzhenCountStart = 0;
        int supStockInItemGdCountStart = 0;
        content.append("（未同步前）近7天入库明细总数:"+supStockInItemListCountStart);
        for (SupStockInItem order:supStockInItemList) {
            if(order.getOrderCode().startsWith("D")){
                shenzhenOrderNumSet.add(order.getOrderCode());
                supStockInItemShenzhenCountStart++;
            }
            if(order.getOrderCode().startsWith("22")){
                gdOrderNumSet.add(order.getOrderCode());
                supStockInItemGdCountStart++;
            }
        }
        content.append("（未同步前）近7天深圳平台入库明细总数:"+supStockInItemShenzhenCountStart);
        content.append("（未同步前）近7天广东平台入库明细总数:"+supStockInItemGdCountStart+"\n ");

        //发票数据
        QueryWrapper<SupInvoiceItem> supInvoiceItemQueryWrapper = new QueryWrapper<>();
        supInvoiceItemQueryWrapper.ge("INVOICE_DATE",this.historyTime7());
        List<SupInvoiceItem> supInvoiceItemList = supInvoiceItemServiceImpl.list(supInvoiceItemQueryWrapper);
        int supInvoiceItemListCountStart = supInvoiceItemList.size();
        int supInvoiceItemShenzhenCountStart = 0;
        int supInvoiceItemGdCountStart = 0;
        content.append("（未同步前）近7天发票明细总数:"+supInvoiceItemListCountStart);
        for (SupInvoiceItem order:supInvoiceItemList) {
            if(order.getOrderCode().startsWith("D")){
                shenzhenOrderNumSet.add(order.getOrderCode());
                supInvoiceItemShenzhenCountStart++;
            }
            if(order.getOrderCode().startsWith("22")){
                gdOrderNumSet.add(order.getOrderCode());
                supInvoiceItemGdCountStart++;
            }
        }
        content.append("（未同步前）近7天深圳平台发票明细总数:"+supInvoiceItemShenzhenCountStart);
        content.append("（未同步前）近7天广东平台发票明细总数:"+supInvoiceItemGdCountStart+"\n ");;


        logger.debug("【同步数据对接定时任务】---处理对接方未同步数据开始执行");
        logger.info("执行时间："+dft.format(new Date()));
        content.append("执行同步开始时间:"+dft.format(new Date())+"\n ");;
        for (String order:shenzhenOrderNumSet) {
            shenzhenMap.put("orderNums",order);
            dealFailSZInterfaceTaskImpl.dealData4(shenzhenMap);
        }
        for (String order:gdOrderNumSet) {
            gdMap.put("orderNums",order);
            dealFailGDInterfaceTaskImpl.dealData4(gdMap);
        }

        //执行完毕，核对数据
        SupOrderItemList = supOrderServiceImpl.list(supOrderItemQueryWrapper);
        int supOrderItemListCountEnd = SupOrderItemList.size();
        int supOrderItemShenzhenCountEnd = 0;
        int supOrderItemGdCountEnd = 0;
        content.append("（同步后）近7天订单明细总数:"+supOrderItemListCountEnd);
        for (SupOrder order:SupOrderItemList) {
            if(order.getOrderNum().startsWith("D")){
                shenzhenOrderNumSet.add(order.getOrderNum());
                supOrderItemShenzhenCountEnd++;
            }
            if(order.getOrderNum().startsWith("22")){
                gdOrderNumSet.add(order.getOrderNum());
                supOrderItemGdCountEnd++;
            }
        }
        content.append("（同步后）近7天深圳平台订单明细总数:"+supOrderItemShenzhenCountEnd);
        content.append("（同步后）近7天广东平台订单明细总数:"+supOrderItemGdCountEnd+"\n ");

        //配送数据
        supDeliveryItemList = supDeliveryItemServiceImpl.list(supDeliveryItemQueryWrapper);
        int supDeliveryItemListCountEnd = supDeliveryItemList.size();
        int supDeliveryItemShenzhenCountEnd = 0;
        int supDeliveryItemGdCountEnd = 0;
        content.append("（同步后）近7天配送明细总数:"+supDeliveryItemListCountEnd);
        for (SupDeliveryItem order:supDeliveryItemList) {
            if(order.getOrderCode().startsWith("D")){//深圳平台订单以D开头
                shenzhenOrderNumSet.add(order.getOrderCode());
                supDeliveryItemShenzhenCountEnd++;
            }
            if(order.getOrderCode().startsWith("22")){//省平台订单以22开头
                gdOrderNumSet.add(order.getOrderCode());
                supDeliveryItemGdCountEnd++;
            }
        }
        content.append("（同步后）近7天深圳平台配送明细总数:"+supDeliveryItemShenzhenCountEnd);
        content.append("（同步后）近7天广东平台配送明细总数:"+supDeliveryItemGdCountEnd+"\n ");

        //入库数据
        supStockInItemList = supStockInItemServiceImpl.list(supStockInItemQueryWrapper);
        int supStockInItemListCountEnd = supStockInItemList.size();
        int supStockInItemShenzhenCountEnd = 0;
        int supStockInItemGdCountEnd = 0;
        content.append("（同步后）近7天入库明细总数:"+supStockInItemListCountEnd);
        for (SupStockInItem order:supStockInItemList) {
            if(order.getOrderCode().startsWith("D")){
                shenzhenOrderNumSet.add(order.getOrderCode());
                supStockInItemShenzhenCountEnd++;
            }
            if(order.getOrderCode().startsWith("22")){
                gdOrderNumSet.add(order.getOrderCode());
                supStockInItemGdCountEnd++;
            }
        }
        content.append("（同步后）近7天深圳平台入库明细总数:"+supStockInItemShenzhenCountEnd);
        content.append("（同步后）近7天广东平台入库明细总数:"+supStockInItemGdCountEnd+"\n ");

        //发票数据
        supInvoiceItemList = supInvoiceItemServiceImpl.list(supInvoiceItemQueryWrapper);
        int supInvoiceItemListCountEnd = supInvoiceItemList.size();
        int supInvoiceItemShenzhenCountEnd = 0;
        int supInvoiceItemGdCountEnd = 0;
        content.append("（同步后）近7天发票明细总数:"+supInvoiceItemListCountEnd);
        for (SupInvoiceItem order:supInvoiceItemList) {
            if(order.getOrderCode().startsWith("D")){
                shenzhenOrderNumSet.add(order.getOrderCode());
                supInvoiceItemShenzhenCountEnd++;
            }
            if(order.getOrderCode().startsWith("22")){
                gdOrderNumSet.add(order.getOrderCode());
                supInvoiceItemGdCountEnd++;
            }
        }
        content.append("（同步后）近7天深圳平台发票明细总数:"+supInvoiceItemShenzhenCountEnd);
        content.append("（同步后）近7天广东平台发票明细总数:"+supInvoiceItemGdCountEnd+"\n ");

        content.append("执行同步结束时间:"+dft.format(new Date()));;

        logger.info("准备发送公文");
        this.officialSuperManager(String.valueOf(content));

    }

    /**
     * 发公文给超级管理员
     */
    public void officialSuperManager(String content){
        try {
            logger.info("开始发送公文");
            String nowDate = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
            //发件人默认超级管理员
            PubUser pubUser = pubUserServiceImpl.getOne(new QueryWrapper<PubUser>().eq("NAME", "超级管理员"));

            SupOfficial supOfficial = new SupOfficial();
            supOfficial.setId(Tools.genId() + "");
            supOfficial.setContent(content);
            supOfficial.setTitle("通知超级管理员数据同步情况");
            supOfficial.setStatus("1");
            supOfficial.setNum(0);
            supOfficial.setSendTime(new Date());
            supOfficial.setCreationTime(new Date());
            supOfficial.setLastModifitionTime(new Date());
            supOfficial.setSendUserId(pubUser.getId());
            supOfficial.setSendUserName(pubUser.getName());
            supOfficial.setCreator(pubUser.getId());
            supOfficial.setLastModifitor(pubUser.getId());


            SupOfficialReceive supOfficialReceive = new SupOfficialReceive();
            supOfficialReceive.setCreator(pubUser.getId());
            supOfficialReceive.setIsSee("0");
            supOfficialReceive.setCreationTime(new Date());
            supOfficialReceive.setStatus("1");
            supOfficialReceive.setOfficialId(supOfficial.getId());
            supOfficialReceive.setUserId(pubUser.getId());
            supOfficialReceive.setId(genId()+"");

            supOfficialReceiveServiceImpl.save(supOfficialReceive);
            supOfficialServiceImpl.save(supOfficial);
        } catch (Exception e) {
            logger.error("公文发送失败，失败原因->{}",e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 获取90天前的日期
     * @return
     */
    public static String historyTime7(){
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");//设置日期格式
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.add(Calendar.DATE, - 7);
        Date d = calendar.getTime();
        String startTime = df.format(d);//7天前日期
        return  startTime;
    }


    /**
     *
     * 由于对接方，存在数据未同步过来的情况
     * 每周再同步一次本周内的订单、配送、入库、发票数据
     */
    public void orderSynchronizeDealData(){
        /***
         * orderSynchronizeDealData、统计近7天数据库中有多少条数据
         * 2、重新远程拉取近7天数据进行同步
         * 3、两个进行对比计算多的条数
         */
        logger.info("每周日定时任务开始执行");
        Set<String> shenzhenOrderNumSet = new HashSet<>();//深圳订单、配送、入库、发票相关订单都放里面
        Set<String> gdOrderNumSet = new HashSet<>();//广东订单、配送、入库、发票相关订单都放里面
        Set<String> gzOrderNumSet = new HashSet<>();//广州订单、配送、入库、发票相关订单都放里面
//        Map<String, Object> shenzhenMap = new HashMap<>();
//        Map<String, Object> gdMap = new HashMap<>();
        StringBuilder content = new StringBuilder(3000);
        SimpleDateFormat dft = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        //订单数据
        QueryWrapper<SupOrder> supOrderQueryWrapper = new QueryWrapper<>();
        supOrderQueryWrapper.ge("SUBMIT_TIME",HistoryTimeUtils.getHistoryTime7().get("startTime"));
        supOrderQueryWrapper.le("SUBMIT_TIME",HistoryTimeUtils.getHistoryTime7().get("endTime"));
        supOrderQueryWrapper.in("SOURCE","1","2","3");
        List<SupOrder> supOrderList = supOrderServiceImpl.list(supOrderQueryWrapper);
        int supOrderListCountStart = supOrderList.size();
        int supOrderShenzhenCountStart = 0;
        int supOrderGdCountStart = 0;
        int supOrderGzCountStart = 0; //广州
        content.append("同步本周内的药品、订单、订单明细、配送、入库、发票数据信息如下："+"\n ");
        content.append("（未同步前）近7天订单总数:"+supOrderListCountStart);
        for (SupOrder order:supOrderList) {
            if(order.getOrderNum().startsWith("D")){
                shenzhenOrderNumSet.add(order.getOrderNum());
                supOrderShenzhenCountStart++;
            }
            if(order.getOrderNum().startsWith("22")){
                gdOrderNumSet.add(order.getOrderNum());
                supOrderGdCountStart++;
            }
            if(order.getOrderNum().length()  == 24){
                gzOrderNumSet.add(order.getOrderNum());
                supOrderGzCountStart++;

            }
        }
        content.append("（未同步前）近7天深圳平台订单总数:"+supOrderShenzhenCountStart);
        content.append("（未同步前）近7天广州平台订单总数:"+supOrderGzCountStart);
        content.append("（未同步前）近7天广东平台订单总数:"+supOrderGdCountStart+"\n ");




        //订单明细数据
        QueryWrapper<SupOrderItem> supOrderItemQueryWrapper = new QueryWrapper<>();
        supOrderItemQueryWrapper.ge("SUBMIT_TIME",HistoryTimeUtils.getHistoryTime7().get("startTime"));
        supOrderItemQueryWrapper.le("SUBMIT_TIME",HistoryTimeUtils.getHistoryTime7().get("endTime"));
        supOrderItemQueryWrapper.in("SOURCE","1","2","3");
        List<SupOrderItem> supOrderItemList = supOrderItemServiceImpl.list(supOrderItemQueryWrapper);
        int supOrderItemListCountStart = supOrderItemList.size();
        int supOrderItemShenzhenCountStart = 0;
        int supOrderItemGdCountStart = 0;
        int supOrderItemGzCountStart = 0; //广州

        content.append("（未同步前）近7天订单明细总数:"+supOrderItemListCountStart);
        for (SupOrderItem order:supOrderItemList) {
            if(order.getOrderNum().startsWith("D")){
                shenzhenOrderNumSet.add(order.getOrderNum());
                supOrderItemShenzhenCountStart++;
            }
            if(order.getOrderNum().startsWith("22")){
                gdOrderNumSet.add(order.getOrderNum());
                supOrderItemGdCountStart++;
            }
            if(order.getOrderNum().length()  == 24){
                gzOrderNumSet.add(order.getOrderNum());
                supOrderItemGzCountStart++;

            }
        }
        content.append("（未同步前）近7天深圳平台订单明细总数:"+supOrderItemShenzhenCountStart);
        content.append("（未同步前）近7天广州平台订单明细总数:"+supOrderItemGzCountStart);
        content.append("（未同步前）近7天广东平台订单明细总数:"+supOrderItemGdCountStart+"\n ");





        //配送数据
        QueryWrapper<SupDeliveryItem> supDeliveryItemQueryWrapper = new QueryWrapper<>();
        supDeliveryItemQueryWrapper.ge("DELIVERY_TIME",HistoryTimeUtils.getHistoryTime7().get("startTime"));
        supDeliveryItemQueryWrapper.le("DELIVERY_TIME",HistoryTimeUtils.getHistoryTime7().get("endTime"));
        List<SupDeliveryItem> supDeliveryItemList = supDeliveryItemServiceImpl.list(supDeliveryItemQueryWrapper);
        int supDeliveryItemListCountStart = supDeliveryItemList.size();
        int supDeliveryItemShenzhenCountStart = 0;
        int supDeliveryItemGdCountStart = 0;
        int supDeliveryItemGzCountStart = 0; //广州
        content.append("（未同步前）近7天配送明细总数:"+supDeliveryItemListCountStart);
        for (SupDeliveryItem order:supDeliveryItemList) {
            if(order.getOrderCode().startsWith("D")){//深圳平台订单以D开头
                shenzhenOrderNumSet.add(order.getOrderCode());
                supDeliveryItemShenzhenCountStart++;
            }
            if(order.getOrderCode().startsWith("22")){//省平台订单以22开头
                gdOrderNumSet.add(order.getOrderCode());
                supDeliveryItemGdCountStart++;
            }
            if(order.getOrderCode().length()  == 24){
                gzOrderNumSet.add(order.getOrderCode());
                supDeliveryItemGzCountStart++;

            }
        }
        content.append("（未同步前）近7天深圳平台配送明细总数:"+supDeliveryItemShenzhenCountStart);
        content.append("（未同步前）近7天广州平台配送明细总数:"+supDeliveryItemGzCountStart);
        content.append("（未同步前）近7天广东平台配送明细总数:"+supDeliveryItemGdCountStart+"\n ");
//
        //入库数据
        QueryWrapper<SupStockInItem> supStockInItemQueryWrapper = new QueryWrapper<>();
        supStockInItemQueryWrapper.ge("STOCK_IN_TIME",HistoryTimeUtils.getHistoryTime7().get("startTime"));
        supStockInItemQueryWrapper.le("STOCK_IN_TIME",HistoryTimeUtils.getHistoryTime7().get("endTime"));
        List<SupStockInItem> supStockInItemList = supStockInItemServiceImpl.list(supStockInItemQueryWrapper);
        int supStockInItemListCountStart = supStockInItemList.size();
        int supStockInItemShenzhenCountStart = 0;
        int supStockInItemGdCountStart = 0;
        int supStockInItemGzCountStart = 0;
        content.append("（未同步前）近7天入库明细总数:"+supStockInItemListCountStart);
        for (SupStockInItem order:supStockInItemList) {
            if(order.getOrderCode().startsWith("D")){
                shenzhenOrderNumSet.add(order.getOrderCode());
                supStockInItemShenzhenCountStart++;
            }
            if(order.getOrderCode().startsWith("22")){
                gdOrderNumSet.add(order.getOrderCode());
                supStockInItemGdCountStart++;
            }
            if(order.getOrderCode().length()  == 24){
                gzOrderNumSet.add(order.getOrderCode());
                supStockInItemGzCountStart++;

            }
        }
        content.append("（未同步前）近7天深圳平台入库明细总数:"+supStockInItemShenzhenCountStart);
        content.append("（未同步前）近7天广州平台入库明细总数:"+supStockInItemGzCountStart);
        content.append("（未同步前）近7天广东平台入库明细总数:"+supStockInItemGdCountStart+"\n ");

        //发票数据
        QueryWrapper<SupInvoiceItem> supInvoiceItemQueryWrapper = new QueryWrapper<>();
        supInvoiceItemQueryWrapper.ge("INVOICE_DATE",HistoryTimeUtils.getHistoryTime7().get("startTime"));
        supInvoiceItemQueryWrapper.le("INVOICE_DATE",HistoryTimeUtils.getHistoryTime7().get("endTime"));
        List<SupInvoiceItem> supInvoiceItemList = supInvoiceItemServiceImpl.list(supInvoiceItemQueryWrapper);
        int supInvoiceItemListCountStart = supInvoiceItemList.size();
        int supInvoiceItemShenzhenCountStart = 0;
        int supInvoiceItemGdCountStart = 0;
        int supInvoiceItemGzCountStart = 0;
        content.append("（未同步前）近7天发票明细总数:"+supInvoiceItemListCountStart);
        for (SupInvoiceItem order:supInvoiceItemList) {
            if(order.getOrderCode().startsWith("D")){
                shenzhenOrderNumSet.add(order.getOrderCode());
                supInvoiceItemShenzhenCountStart++;
            }
            if(order.getOrderCode().startsWith("22")){
                gdOrderNumSet.add(order.getOrderCode());
                supInvoiceItemGdCountStart++;
            }
            if(order.getOrderCode().length()  == 24){
                gzOrderNumSet.add(order.getOrderCode());
                supInvoiceItemGzCountStart++;

            }
        }
        content.append("（未同步前）近7天深圳平台发票明细总数:"+supInvoiceItemShenzhenCountStart);
        content.append("（未同步前）近7天广州平台发票明细总数:"+supInvoiceItemGzCountStart);
        content.append("（未同步前）近7天广东平台发票明细总数:"+supInvoiceItemGdCountStart+"\n ");;




        logger.debug("【同步数据对接定时任务】---处理对接方未同步数据开始执行");
        logger.info("执行时间："+dft.format(new Date()));
        content.append("执行同步开始时间:"+dft.format(new Date())+"\n ");;
//        for (String order:shenzhenOrderNumSet) {
//            shenzhenMap.put("orderNums",order);
//        dealFailSZInterfaceTaskImpl.dealData4(shenzhenMap);
//        }

        //深圳开始同步
        shenZhenInterfaceTaskImpl.addHistoryTime7();

        //广州开始同步
        gzInterfaceTaskImpl.addHistoryTime7();

        //广东开始同步
        gdInterfaceTaskImpl.addHistoryTime7();
//        for (String order:gdOrderNumSet) {
//            gdMap.put("orderNums",order);
//            dealFailGDInterfaceTaskImpl.dealData4(gdMap);
//        }
//
        //supOrderList，核对数据
        supOrderList = supOrderServiceImpl.list(supOrderQueryWrapper);
        int supOrderListCountEnd = supOrderList.size();
        int supOrderShenzhenCountEnd = 0;
        int supOrderGdCountEnd = 0;
        int supOrderGzCountEnd = 0;
        content.append("（同步后）近7天订单总数:"+supOrderListCountEnd);
        for (SupOrder order:supOrderList) {
            if(order.getOrderNum().startsWith("D")){
                shenzhenOrderNumSet.add(order.getOrderNum());
                supOrderShenzhenCountEnd++;
            }
            if(order.getOrderNum().startsWith("22")){
                gdOrderNumSet.add(order.getOrderNum());
                supOrderGdCountEnd++;
            }
            if(order.getOrderNum().length()  == 24){
                gzOrderNumSet.add(order.getOrderNum());
                supOrderGzCountEnd++;

            }
        }
        content.append("（同步后）近7天深圳平台订单总数:"+supOrderShenzhenCountEnd);
        content.append("（同步后）近7天广州平台订单总数:"+supOrderGzCountEnd);
        content.append("（同步后）近7天广东平台订单总数:"+supOrderGdCountEnd+"\n ");

        //订单明细
        supOrderItemList = supOrderItemServiceImpl.list(supOrderItemQueryWrapper);
        int supOrderItemListCountEnd = supOrderItemList.size();
        int supOrderItemShenzhenCountEnd = 0;
        int supOrderItemGdCountEnd = 0;
        int supOrderItemGzCountEnd = 0;

        content.append("（同步后）近7天订单明细总数:"+supOrderItemListCountEnd);
        for (SupOrderItem order:supOrderItemList) {
            if(order.getOrderNum().startsWith("D")){
                shenzhenOrderNumSet.add(order.getOrderNum());
                supOrderItemShenzhenCountEnd++;
            }
            if(order.getOrderNum().startsWith("22")){
                gdOrderNumSet.add(order.getOrderNum());
                supOrderItemGdCountEnd++;
            }
            if(order.getOrderNum().length()  == 24){
                gzOrderNumSet.add(order.getOrderNum());
                supOrderItemGzCountEnd++;

            }
        }
        content.append("（同步后）近7天深圳平台订单明细总数:"+supOrderItemShenzhenCountEnd);
        content.append("（同步后）近7天广州平台订单明细总数:"+supOrderItemGzCountEnd);
        content.append("（同步后）近7天广东平台订单明细总数:"+supOrderItemGdCountEnd+"\n ");




        //配送数据
        supDeliveryItemList = supDeliveryItemServiceImpl.list(supDeliveryItemQueryWrapper);
        int supDeliveryItemListCountEnd = supDeliveryItemList.size();
        int supDeliveryItemShenzhenCountEnd = 0;
        int supDeliveryItemGdCountEnd = 0;
        int supDeliveryItemGzCountEnd = 0;
        content.append("（同步后）近7天配送明细总数:"+supDeliveryItemListCountEnd);
        for (SupDeliveryItem order:supDeliveryItemList) {
            if(order.getOrderCode().startsWith("D")){//深圳平台订单以D开头
                shenzhenOrderNumSet.add(order.getOrderCode());
                supDeliveryItemShenzhenCountEnd++;
            }
            if(order.getOrderCode().startsWith("22")){//省平台订单以22开头
                gdOrderNumSet.add(order.getOrderCode());
                supDeliveryItemGdCountEnd++;
            }
            if(order.getOrderCode().length()  == 24){
                gzOrderNumSet.add(order.getOrderCode());
                supDeliveryItemGzCountEnd++;

            }
        }
        content.append("（同步后）近7天深圳平台配送明细总数:"+supDeliveryItemShenzhenCountEnd);
        content.append("（同步后）近7天广州平台配送明细总数:"+supDeliveryItemGzCountEnd);
        content.append("（同步后）近7天广东平台配送明细总数:"+supDeliveryItemGdCountEnd+"\n ");

        //入库数据
        supStockInItemList = supStockInItemServiceImpl.list(supStockInItemQueryWrapper);
        int supStockInItemListCountEnd = supStockInItemList.size();
        int supStockInItemShenzhenCountEnd = 0;
        int supStockInItemGdCountEnd = 0;
        int supStockInItemGzCountEnd = 0;
        content.append("（同步后）近7天入库明细总数:"+supStockInItemListCountEnd);
        for (SupStockInItem order:supStockInItemList) {
            if(order.getOrderCode().startsWith("D")){
                shenzhenOrderNumSet.add(order.getOrderCode());
                supStockInItemShenzhenCountEnd++;
            }
            if(order.getOrderCode().startsWith("22")){
                gdOrderNumSet.add(order.getOrderCode());
                supStockInItemGdCountEnd++;
            }

            if(order.getOrderCode().length()  == 24){
                gzOrderNumSet.add(order.getOrderCode());
                supStockInItemGzCountEnd++;

            }
        }
        content.append("（同步后）近7天深圳平台入库明细总数:"+supStockInItemShenzhenCountEnd);
        content.append("（同步后）近7天广州平台入库明细总数:"+supStockInItemGzCountEnd);
        content.append("（同步后）近7天广东平台入库明细总数:"+supStockInItemGdCountEnd+"\n ");

        //发票数据
        supInvoiceItemList = supInvoiceItemServiceImpl.list(supInvoiceItemQueryWrapper);
        int supInvoiceItemListCountEnd = supInvoiceItemList.size();
        int supInvoiceItemShenzhenCountEnd = 0;
        int supInvoiceItemGdCountEnd = 0;
        int supInvoiceItemGzCountEnd = 0;
        content.append("（同步后）近7天发票明细总数:"+supInvoiceItemListCountEnd);
        for (SupInvoiceItem order:supInvoiceItemList) {
            if(order.getOrderCode().startsWith("D")){
                shenzhenOrderNumSet.add(order.getOrderCode());
                supInvoiceItemShenzhenCountEnd++;
            }
            if(order.getOrderCode().startsWith("22")){
                gdOrderNumSet.add(order.getOrderCode());
                supInvoiceItemGdCountEnd++;
            }
            if(order.getOrderCode().length()  == 24){
                gzOrderNumSet.add(order.getOrderCode());
                supInvoiceItemGzCountEnd++;

            }
        }
        content.append("（同步后）近7天深圳平台发票明细总数:"+supInvoiceItemShenzhenCountEnd);
        content.append("（同步后）近7天广州平台发票明细总数:"+supInvoiceItemGzCountEnd);
        content.append("（同步后）近7天广东平台发票明细总数:"+supInvoiceItemGdCountEnd+"\n ");

        content.append("执行同步结束时间:"+dft.format(new Date()));;

        this.officialSuperManager(String.valueOf(content));

    }


    /**
     * 每周12点执行 判断前一个任务10点的是否执行，
     * 由于每周10点的定时任务不稳定 ，加一个判断是否需要再次拉取数据
     */
    public void retryOrderTask(){
        try {
            logger.info("准备开始执行每周日拉取全药网以及省平台数据重试机制");
            //上一个任务执行的方法名称
            String taskName = "orderSynchronizeDealData";
            //根据方法名称从定时任务表中获取执行的时间
            SupJob supJob =  supJobServiceImpl.selectByTaskName(taskName);
            if(Objects.isNull(supJob)){
                logger.error("每周日数据拉取重试机制失败，失败原因：上一个任务查找失败，方法名称->{}",taskName);
                return;
            }
            //截取执行时间
            String cron = supJob.getCron();
            int index = cron.indexOf("?");
            String cronTime = cron.substring(index - 3, index).trim();
            //获取当前时间
            String format = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
            //时间拼接
            String taskTime = format + " " + cronTime +":00:00";
            //根据执行时间从日志表获取是否有拉取数据记录
            Long count = iInterfaceLogService.selectByTaskTime(taskTime);
            if(count == 0){
                logger.info("上一个任务执行异常，未查询到相关数据，开始数据拉取重试机制");
                this.orderSynchronizeDealData();
                logger.info("每周日数据拉取重试机制数据拉取成功");
                return;
            }
            logger.info("上一个任务拉取执行成功，一共拉取{}条数据，无需重复拉取数据",count);
        } catch (Exception e) {
            logger.error("每周日数据拉取重试机制失败，失败原因->{}",e.getMessage());
            throw new RuntimeException(e);
        }
    }


    /**
     * 广州数据拉取
     */
    public void retryOrderTaskGz(){
        logger.info("准备开始拉取广州数据");

        //现有的订单数据
        QueryWrapper<SupOrder> supOrderQueryWrapper = new QueryWrapper<>();
        supOrderQueryWrapper.ge("SUBMIT_TIME",HistoryTimeUtils.getHistoryTime7().get("startTime"));
        supOrderQueryWrapper.le("SUBMIT_TIME",HistoryTimeUtils.getHistoryTime7().get("endTime"));
        supOrderQueryWrapper.eq("SOURCE","3");
        List<SupOrder> orderList = supOrderServiceImpl.list(supOrderQueryWrapper);

        //数据拉取
        gzInterfaceTaskImpl.addHistoryTime7();

    }

    /**
     * 每天都执行 只有星期天才进行插入操作
     */
    public void toDayTask(){
        logger.info("开始执行每日判断数据拉取任务");
        // 获取当前日期是星期几
        Calendar calendar = Calendar.getInstance();
        int dayOfWeek = calendar.get(Calendar.DAY_OF_WEEK);
        OperationLog operationLog = new OperationLog();
        //获取当前时间
        Date date = new Date();
        String taskTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(date);
        operationLog.setAddStartTime(date);
        String operationLogId = "" + Tools.genId();
        operationLog.setCreateTime(date);
        String message = "开始执行数据拉取任务，当前时间：" + taskTime + ",";
        // 星期名称数组
        String[] weekDays = {"未知", "星期天", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六"};
        if (dayOfWeek == Calendar.SUNDAY) {
//            this.retryOrderTask();
            this.orderSynchronizeDealData();
            //根据执行时间从日志表获取是否有拉取数据记录
            Long count = iInterfaceLogService.selectByTaskTime(taskTime);
            message += "今天是星期天，开始执行每周日定时任务，数据拉取完成，共拉取{}条数据"+count+",具体信息请查看公文推送";
        } else {
            message += "今天不是星期天，当前是：" + weekDays[dayOfWeek] +",无需拉取,定时任务执行结束";
        }
        operationLog.setId(operationLogId);
        operationLog.setCodeType("toDayTask");
        operationLog.setCode("toDayTask");
        operationLog.setOperationName(message);
        operationLog.setAddEndTime(new Date());
        operationLogService.save(operationLog);
    }



}
